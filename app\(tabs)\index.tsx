import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import React, { useState } from 'react';
import { Alert, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// Dummy data untuk simulasi
const DUMMY_DATA = {
  targetBiaya: 35000000, // 35 juta
  totalTabungan: 12500000, // 12.5 juta
  setoranTerakhir: 500000, // 500 ribu
  tanggalSetoranTerakhir: '2024-06-20',
  riwayatSetoran: [
    { id: 1, tanggal: '2024-06-20', jumlah: 500000 },
    { id: 2, tanggal: '2024-06-15', jumlah: 750000 },
    { id: 3, tanggal: '2024-06-10', jumlah: 600000 },
  ]
};

export default function BerandaScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  
  const [data, setData] = useState(DUMMY_DATA);
  
  // Hitung progress persentase
  const progressPercentage = (data.totalTabungan / data.targetBiaya) * 100;
  
  // Estimasi waktu tercapai (asumsi setoran rata-rata per bulan)
  const rataRataSetoranPerBulan = 600000; // dari dummy data
  const sisaTabungan = data.targetBiaya - data.totalTabungan;
  const estimasiBulan = Math.ceil(sisaTabungan / rataRataSetoranPerBulan);
  
  const formatRupiah = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const handleTambahSetoran = () => {
    Alert.alert(
      'Tambah Setoran',
      'Fitur ini akan mengarahkan ke halaman Tabungan untuk menambah setoran baru.',
      [{ text: 'OK' }]
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={[styles.greeting, { color: colors.text }]}>
            Assalamu'alaikum
          </Text>
          <Text style={[styles.title, { color: colors.text }]}>
            Tabungan Haji Anda
          </Text>
        </View>

        {/* Progress Card */}
        <View style={[styles.progressCard, { backgroundColor: colors.tint }]}>
          <View style={styles.progressHeader}>
            <IconSymbol name="target" size={24} color="white" />
            <Text style={styles.progressTitle}>Progress Tabungan</Text>
          </View>
          
          <Text style={styles.progressAmount}>
            {formatRupiah(data.totalTabungan)}
          </Text>
          <Text style={styles.progressTarget}>
            dari {formatRupiah(data.targetBiaya)}
          </Text>
          
          {/* Progress Bar */}
          <View style={styles.progressBarContainer}>
            <View 
              style={[
                styles.progressBar, 
                { width: `${Math.min(progressPercentage, 100)}%` }
              ]} 
            />
          </View>
          <Text style={styles.progressPercentage}>
            {progressPercentage.toFixed(1)}% tercapai
          </Text>
        </View>

        {/* Stats Cards */}
        <View style={styles.statsContainer}>
          <View style={[styles.statCard, { backgroundColor: colors.background }]}>
            <IconSymbol name="calendar" size={20} color={colors.tint} />
            <Text style={[styles.statLabel, { color: colors.text }]}>
              Estimasi Tercapai
            </Text>
            <Text style={[styles.statValue, { color: colors.tint }]}>
              {estimasiBulan} bulan lagi
            </Text>
          </View>
          
          <View style={[styles.statCard, { backgroundColor: colors.background }]}>
            <IconSymbol name="chart.bar.fill" size={20} color={colors.tint} />
            <Text style={[styles.statLabel, { color: colors.text }]}>
              Setoran Terakhir
            </Text>
            <Text style={[styles.statValue, { color: colors.tint }]}>
              {formatRupiah(data.setoranTerakhir)}
            </Text>
          </View>
        </View>

        {/* Action Button */}
        <TouchableOpacity 
          style={[styles.actionButton, { backgroundColor: colors.tint }]}
          onPress={handleTambahSetoran}
        >
          <IconSymbol name="plus.circle.fill" size={24} color="white" />
          <Text style={styles.actionButtonText}>Tambah Setoran</Text>
        </TouchableOpacity>

        {/* Motivational Message */}
        <View style={[styles.motivationCard, { backgroundColor: colors.background }]}>
          <Text style={[styles.motivationTitle, { color: colors.text }]}>
            💪 Tetap Semangat!
          </Text>
          <Text style={[styles.motivationText, { color: colors.text }]}>
            Anda sudah menabung {progressPercentage.toFixed(1)}% dari target. 
            Konsisten menabung akan membawa Anda lebih dekat ke Tanah Suci!
          </Text>
        </View>

        {/* Recent Transactions Preview */}
        <View style={[styles.recentCard, { backgroundColor: colors.background }]}>
          <Text style={[styles.recentTitle, { color: colors.text }]}>
            Setoran Terbaru
          </Text>
          {data.riwayatSetoran.slice(0, 3).map((item) => (
            <View key={item.id} style={styles.recentItem}>
              <Text style={[styles.recentDate, { color: colors.text }]}>
                {new Date(item.tanggal).toLocaleDateString('id-ID')}
              </Text>
              <Text style={[styles.recentAmount, { color: colors.tint }]}>
                {formatRupiah(item.jumlah)}
              </Text>
            </View>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  greeting: {
    fontSize: 16,
    opacity: 0.7,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 4,
  },
  progressCard: {
    margin: 20,
    padding: 20,
    borderRadius: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  progressHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  progressTitle: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  progressAmount: {
    color: 'white',
    fontSize: 28,
    fontWeight: 'bold',
  },
  progressTarget: {
    color: 'white',
    fontSize: 14,
    opacity: 0.9,
    marginBottom: 16,
  },
  progressBarContainer: {
    height: 8,
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 4,
    marginBottom: 8,
  },
  progressBar: {
    height: '100%',
    backgroundColor: 'white',
    borderRadius: 4,
  },
  progressPercentage: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    gap: 12,
  },
  statCard: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  statLabel: {
    fontSize: 12,
    marginTop: 8,
    marginBottom: 4,
  },
  statValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    margin: 20,
    padding: 16,
    borderRadius: 12,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  motivationCard: {
    margin: 20,
    marginTop: 0,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  motivationTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  motivationText: {
    fontSize: 14,
    lineHeight: 20,
    opacity: 0.8,
  },
  recentCard: {
    margin: 20,
    marginTop: 0,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  recentTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  recentItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  recentDate: {
    fontSize: 14,
  },
  recentAmount: {
    fontSize: 14,
    fontWeight: '600',
  },
});
