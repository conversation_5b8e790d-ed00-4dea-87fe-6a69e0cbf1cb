import { useState, useEffect } from 'react';
import { userStorage, targetStorage, UserData, TargetData } from '@/utils/storage';
import { DUMMY_USER_DATA, DUMMY_TARGET_DATA } from '@/data/dummyData';

export function useUserData() {
  const [userData, setUserData] = useState<UserData | null>(null);
  const [targetData, setTargetData] = useState<TargetData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load data on mount
  useEffect(() => {
    loadUserData();
  }, []);

  const loadUserData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Load user data
      let user = await userStorage.getUserData();
      if (!user) {
        user = DUMMY_USER_DATA;
        await userStorage.saveUserData(user);
      }
      setUserData(user);

      // Load target data
      let target = await targetStorage.getTargetData();
      if (!target) {
        target = DUMMY_TARGET_DATA;
        await targetStorage.saveTargetData(target);
      }
      setTargetData(target);
      
    } catch (err) {
      setError('Gagal memuat data pengguna');
      console.error('Error loading user data:', err);
    } finally {
      setLoading(false);
    }
  };

  const updateUserData = async (newUserData: UserData) => {
    try {
      setError(null);
      
      await userStorage.saveUserData(newUserData);
      setUserData(newUserData);
      
      return { success: true };
    } catch (err) {
      const errorMessage = 'Gagal memperbarui data pengguna';
      setError(errorMessage);
      console.error('Error updating user data:', err);
      return { success: false, error: errorMessage };
    }
  };

  const updateTargetData = async (newTargetData: TargetData) => {
    try {
      setError(null);
      
      await targetStorage.saveTargetData(newTargetData);
      setTargetData(newTargetData);
      
      return { success: true };
    } catch (err) {
      const errorMessage = 'Gagal memperbarui target haji';
      setError(errorMessage);
      console.error('Error updating target data:', err);
      return { success: false, error: errorMessage };
    }
  };

  const refreshData = () => {
    loadUserData();
  };

  // Calculate target progress
  const getTargetProgress = (currentAmount: number) => {
    if (!targetData) return null;

    const { targetBiaya, tanggalTarget } = targetData;
    const progress = (currentAmount / targetBiaya) * 100;
    const remaining = Math.max(0, targetBiaya - currentAmount);
    
    // Calculate time remaining
    const targetDate = new Date(tanggalTarget);
    const now = new Date();
    const timeRemaining = targetDate.getTime() - now.getTime();
    const daysRemaining = Math.ceil(timeRemaining / (1000 * 60 * 60 * 24));
    const monthsRemaining = Math.ceil(daysRemaining / 30);
    
    // Calculate required monthly saving
    const requiredMonthly = monthsRemaining > 0 ? remaining / monthsRemaining : 0;
    
    return {
      progress: Math.min(progress, 100),
      remaining,
      daysRemaining: Math.max(0, daysRemaining),
      monthsRemaining: Math.max(0, monthsRemaining),
      requiredMonthly,
      isTargetReached: currentAmount >= targetBiaya,
      isOverdue: daysRemaining < 0,
    };
  };

  return {
    userData,
    targetData,
    loading,
    error,
    updateUserData,
    updateTargetData,
    refreshData,
    getTargetProgress,
  };
}
