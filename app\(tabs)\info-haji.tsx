import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  Linking,
  Alert 
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

// Data informasi haji
const SYARAT_HAJI = [
  'Beragama Islam',
  'Baligh (dewasa) dan berakal sehat',
  'Mampu secara finansial (istitha\'ah)',
  'Mampu secara fisik dan kesehatan',
  'Memi<PERSON>i mahram (untuk wanita)',
  'Tidak memiliki hutang yang mendesak',
];

const RUKUN_HAJI = [
  'Ihram',
  'Wukuf di Arafah',
  'Tawaf Ifadhah',
  'Sa\'i antara Shafa dan <PERSON>h',
];

const BIAYA_ESTIMASI = [
  { jenis: 'Haji <PERSON>uler', biaya: 'Rp 35.000.000', keterangan: 'Paket standar dengan fasilitas dasar' },
  { jenis: 'Haji Plus', biaya: 'Rp 50.000.000', keterangan: 'Fasilitas lebih baik dan nyaman' },
  { jenis: 'Haji Khusus', biaya: 'Rp 75.000.000', keterangan: 'Fasilitas premium dan eksklusif' },
];

const TIPS_PERSIAPAN = [
  {
    title: 'Persiapan Finansial',
    items: [
      'Mulai menabung sedini mungkin',
      'Buat target tabungan yang realistis',
      'Manfaatkan investasi syariah',
      'Siapkan dana darurat tambahan'
    ]
  },
  {
    title: 'Persiapan Fisik',
    items: [
      'Jaga kesehatan dan stamina',
      'Lakukan medical check-up rutin',
      'Latihan berjalan jarak jauh',
      'Konsumsi makanan bergizi'
    ]
  },
  {
    title: 'Persiapan Mental & Spiritual',
    items: [
      'Pelajari manasik haji',
      'Tingkatkan ibadah dan dzikir',
      'Mohon doa dari keluarga',
      'Niatkan haji karena Allah SWT'
    ]
  }
];

export default function InfoHajiScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  const handleOpenKemenag = async () => {
    const url = 'https://haji.kemenag.go.id/';
    const supported = await Linking.canOpenURL(url);
    
    if (supported) {
      await Linking.openURL(url);
    } else {
      Alert.alert('Error', 'Tidak dapat membuka link. Silakan buka browser dan kunjungi haji.kemenag.go.id');
    }
  };

  const handleOpenSiskohat = async () => {
    const url = 'https://siskohat.kemenag.go.id/';
    const supported = await Linking.canOpenURL(url);
    
    if (supported) {
      await Linking.openURL(url);
    } else {
      Alert.alert('Error', 'Tidak dapat membuka link. Silakan buka browser dan kunjungi siskohat.kemenag.go.id');
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={[styles.title, { color: colors.text }]}>
            Informasi Haji
          </Text>
          <Text style={[styles.subtitle, { color: colors.text }]}>
            Panduan lengkap persiapan haji
          </Text>
        </View>

        {/* Syarat Haji */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            📋 Syarat Wajib Haji
          </Text>
          <View style={[styles.card, { backgroundColor: colors.background }]}>
            {SYARAT_HAJI.map((syarat, index) => (
              <View key={index} style={styles.listItem}>
                <Text style={[styles.bullet, { color: colors.tint }]}>•</Text>
                <Text style={[styles.listText, { color: colors.text }]}>
                  {syarat}
                </Text>
              </View>
            ))}
          </View>
        </View>

        {/* Rukun Haji */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            🕋 Rukun Haji
          </Text>
          <View style={[styles.card, { backgroundColor: colors.background }]}>
            {RUKUN_HAJI.map((rukun, index) => (
              <View key={index} style={styles.listItem}>
                <View style={[styles.numberBadge, { backgroundColor: colors.tint }]}>
                  <Text style={styles.numberText}>{index + 1}</Text>
                </View>
                <Text style={[styles.listText, { color: colors.text }]}>
                  {rukun}
                </Text>
              </View>
            ))}
          </View>
        </View>

        {/* Estimasi Biaya */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            💰 Estimasi Biaya Haji 2024
          </Text>
          {BIAYA_ESTIMASI.map((item, index) => (
            <View key={index} style={[styles.biayaCard, { backgroundColor: colors.background }]}>
              <View style={styles.biayaHeader}>
                <Text style={[styles.biayaJenis, { color: colors.text }]}>
                  {item.jenis}
                </Text>
                <Text style={[styles.biayaAmount, { color: colors.tint }]}>
                  {item.biaya}
                </Text>
              </View>
              <Text style={[styles.biayaKeterangan, { color: colors.text }]}>
                {item.keterangan}
              </Text>
            </View>
          ))}
        </View>

        {/* Tips Persiapan */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            💡 Tips Persiapan Haji
          </Text>
          {TIPS_PERSIAPAN.map((tip, index) => (
            <View key={index} style={[styles.card, { backgroundColor: colors.background }]}>
              <Text style={[styles.tipTitle, { color: colors.tint }]}>
                {tip.title}
              </Text>
              {tip.items.map((item, itemIndex) => (
                <View key={itemIndex} style={styles.listItem}>
                  <Text style={[styles.bullet, { color: colors.tint }]}>•</Text>
                  <Text style={[styles.listText, { color: colors.text }]}>
                    {item}
                  </Text>
                </View>
              ))}
            </View>
          ))}
        </View>

        {/* Link Kemenag */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            🔗 Link Penting
          </Text>
          
          <TouchableOpacity 
            style={[styles.linkCard, { backgroundColor: colors.tint }]}
            onPress={handleOpenKemenag}
          >
            <View style={styles.linkContent}>
              <IconSymbol name="info.circle.fill" size={24} color="white" />
              <View style={styles.linkText}>
                <Text style={styles.linkTitle}>Portal Haji Kemenag</Text>
                <Text style={styles.linkSubtitle}>
                  Informasi resmi dan pendaftaran haji
                </Text>
              </View>
            </View>
            <IconSymbol name="chevron.right" size={20} color="white" />
          </TouchableOpacity>

          <TouchableOpacity 
            style={[styles.linkCard, { backgroundColor: colors.background }]}
            onPress={handleOpenSiskohat}
          >
            <View style={styles.linkContent}>
              <IconSymbol name="calendar" size={24} color={colors.tint} />
              <View style={styles.linkText}>
                <Text style={[styles.linkTitle, { color: colors.text }]}>
                  Siskohat Kemenag
                </Text>
                <Text style={[styles.linkSubtitle, { color: colors.text }]}>
                  Sistem informasi dan komputerisasi haji terpadu
                </Text>
              </View>
            </View>
            <IconSymbol name="chevron.right" size={20} color={colors.tint} />
          </TouchableOpacity>
        </View>

        {/* Doa */}
        <View style={styles.section}>
          <View style={[styles.doaCard, { backgroundColor: colors.tint }]}>
            <Text style={styles.doaTitle}>🤲 Doa Niat Haji</Text>
            <Text style={styles.doaArabic}>
              لَبَّيْكَ اللَّهُمَّ حَجًّا
            </Text>
            <Text style={styles.doaTranslation}>
              "Labbaika Allahumma hajjan"
            </Text>
            <Text style={styles.doaArtinya}>
              Artinya: "Aku penuhi panggilan-Mu ya Allah untuk berhaji"
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  subtitle: {
    fontSize: 14,
    opacity: 0.7,
    marginTop: 4,
  },
  section: {
    padding: 20,
    paddingTop: 10,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  card: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  bullet: {
    fontSize: 16,
    marginRight: 8,
    marginTop: 2,
  },
  listText: {
    fontSize: 14,
    flex: 1,
    lineHeight: 20,
  },
  numberBadge: {
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
    marginTop: 2,
  },
  numberText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  biayaCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  biayaHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  biayaJenis: {
    fontSize: 16,
    fontWeight: '600',
  },
  biayaAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  biayaKeterangan: {
    fontSize: 12,
    opacity: 0.7,
  },
  tipTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  linkCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  linkContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  linkText: {
    marginLeft: 12,
    flex: 1,
  },
  linkTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  linkSubtitle: {
    fontSize: 12,
    color: 'white',
    opacity: 0.9,
    marginTop: 2,
  },
  doaCard: {
    padding: 20,
    borderRadius: 16,
    alignItems: 'center',
  },
  doaTitle: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
  },
  doaArabic: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  doaTranslation: {
    color: 'white',
    fontSize: 14,
    fontStyle: 'italic',
    marginBottom: 8,
  },
  doaArtinya: {
    color: 'white',
    fontSize: 12,
    textAlign: 'center',
    opacity: 0.9,
  },
});
