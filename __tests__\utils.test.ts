// Basic tests for utility functions
import { 
  formatRupiah, 
  formatDate, 
  formatRelativeTime, 
  parseRupiah,
  formatPercentage,
  calculateMonthsDifference,
  formatDuration 
} from '@/utils/formatters';

// Mock test runner (since we don't have Jest configured)
const test = (name: string, fn: () => void) => {
  try {
    fn();
    console.log(`✅ ${name}`);
  } catch (error) {
    console.error(`❌ ${name}:`, error);
  }
};

const expect = (actual: any) => ({
  toBe: (expected: any) => {
    if (actual !== expected) {
      throw new Error(`Expected ${expected}, but got ${actual}`);
    }
  },
  toContain: (expected: any) => {
    if (!actual.includes(expected)) {
      throw new Error(`Expected ${actual} to contain ${expected}`);
    }
  },
});

// Run tests
export const runUtilityTests = () => {
  console.log('🧪 Running Utility Tests...\n');

  test('formatRupiah should format numbers correctly', () => {
    expect(formatRupiah(1000000)).toBe('Rp1.000.000');
    expect(formatRupiah(500000)).toBe('Rp500.000');
    expect(formatRupiah(0)).toBe('Rp0');
  });

  test('formatDate should format dates correctly', () => {
    const result = formatDate('2024-06-24');
    expect(result).toContain('Juni');
    expect(result).toContain('2024');
  });

  test('formatRelativeTime should work for recent dates', () => {
    const today = new Date().toISOString().split('T')[0];
    expect(formatRelativeTime(today)).toBe('Hari ini');
    
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    expect(formatRelativeTime(yesterday.toISOString().split('T')[0])).toBe('Kemarin');
  });

  test('parseRupiah should parse currency strings', () => {
    expect(parseRupiah('Rp1.000.000')).toBe(1000000);
    expect(parseRupiah('500.000')).toBe(500000);
    expect(parseRupiah('invalid')).toBe(0);
  });

  test('formatPercentage should format percentages', () => {
    expect(formatPercentage(75.5)).toBe('75.5%');
    expect(formatPercentage(100, 0)).toBe('100%');
  });

  test('calculateMonthsDifference should calculate correctly', () => {
    expect(calculateMonthsDifference('2024-01-01', '2024-12-31')).toBe(11);
    expect(calculateMonthsDifference('2024-06-01', '2025-06-01')).toBe(12);
  });

  test('formatDuration should format durations correctly', () => {
    expect(formatDuration(6)).toBe('6 bulan');
    expect(formatDuration(12)).toBe('1 tahun');
    expect(formatDuration(18)).toBe('1 tahun 6 bulan');
  });

  console.log('\n✨ Utility tests completed!');
};

// Test data validation
export const runDataValidationTests = () => {
  console.log('🧪 Running Data Validation Tests...\n');

  test('should validate user data structure', () => {
    const userData = {
      nama: 'Test User',
      email: '<EMAIL>',
      telepon: '081234567890',
      alamat: 'Test Address',
      tanggalLahir: '1990-01-01',
    };

    expect(typeof userData.nama).toBe('string');
    expect(typeof userData.email).toBe('string');
    expect(userData.email).toContain('@');
    expect(typeof userData.telepon).toBe('string');
    expect(typeof userData.alamat).toBe('string');
    expect(typeof userData.tanggalLahir).toBe('string');
  });

  test('should validate target data structure', () => {
    const targetData = {
      targetBiaya: 35000000,
      tanggalTarget: '2026-12-31',
      paketHaji: 'reguler' as const,
    };

    expect(typeof targetData.targetBiaya).toBe('number');
    expect(targetData.targetBiaya > 0).toBe(true);
    expect(typeof targetData.tanggalTarget).toBe('string');
    expect(['reguler', 'plus', 'khusus'].includes(targetData.paketHaji)).toBe(true);
  });

  test('should validate setoran data structure', () => {
    const setoranData = {
      id: 1,
      tanggal: '2024-06-24',
      jumlah: 500000,
      keterangan: 'Test setoran',
    };

    expect(typeof setoranData.id).toBe('number');
    expect(typeof setoranData.tanggal).toBe('string');
    expect(typeof setoranData.jumlah).toBe('number');
    expect(setoranData.jumlah > 0).toBe(true);
    expect(typeof setoranData.keterangan).toBe('string');
  });

  console.log('\n✨ Data validation tests completed!');
};

// Test business logic
export const runBusinessLogicTests = () => {
  console.log('🧪 Running Business Logic Tests...\n');

  test('should calculate progress correctly', () => {
    const target = 35000000;
    const current = 17500000;
    const progress = (current / target) * 100;
    
    expect(progress).toBe(50);
  });

  test('should calculate remaining amount correctly', () => {
    const target = 35000000;
    const current = 12500000;
    const remaining = target - current;
    
    expect(remaining).toBe(22500000);
  });

  test('should calculate monthly requirement correctly', () => {
    const remaining = 22500000;
    const monthsLeft = 30;
    const monthlyRequired = remaining / monthsLeft;
    
    expect(monthlyRequired).toBe(750000);
  });

  test('should handle edge cases', () => {
    // Target already reached
    const target = 35000000;
    const current = 40000000;
    const progress = Math.min((current / target) * 100, 100);
    
    expect(progress).toBe(100);
    
    // Zero values
    expect((0 / target) * 100).toBe(0);
  });

  console.log('\n✨ Business logic tests completed!');
};

// Run all tests
export const runAllTests = () => {
  console.log('🚀 Starting Test Suite...\n');
  
  runUtilityTests();
  runDataValidationTests();
  runBusinessLogicTests();
  
  console.log('\n🎉 All tests completed!');
};
